package com.maguo.loan.cash.flow.entrance.common.service;

import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import com.maguo.loan.cash.flow.entity.common.ProjectElementsExt;
import com.maguo.loan.cash.flow.entity.common.ProjectContractCapital;
import com.maguo.loan.cash.flow.entity.common.ProjectContractFlow;
import com.maguo.loan.cash.flow.entity.vo.ProjectInfoVO;
import com.maguo.loan.cash.flow.repository.ProjectInfoRepository;
import com.maguo.loan.cash.flow.repository.ProjectElementsRepository;
import com.maguo.loan.cash.flow.repository.ProjectElementsExtRepository;
import com.maguo.loan.cash.flow.repository.ProjectContractCapitalRepository;
import com.maguo.loan.cash.flow.repository.ProjectContractFlowRepository;
import com.maguo.loan.cash.flow.service.CacheService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 项目相关服务
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 14:11
 */
@Service
public class ProjectInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectInfoService.class);

    @Autowired
    private ProjectInfoRepository projectInfoRepository;

    @Autowired
    private ProjectElementsRepository projectElementsRepository;

    @Autowired
    private ProjectElementsExtRepository projectElementsExtRepository;

    @Autowired
    private ProjectContractCapitalRepository projectContractCapitalRepository;

    @Autowired
    private ProjectContractFlowRepository projectContractFlowRepository;

    @Autowired
    private CacheService cacheService;

    /**
     * 项目信息缓存key前缀
     */
    private static final String PROJECT_INFO_CACHE_PREFIX = "PROJECT_INFO_";

    /**
     * 缓存过期时间：2小时
     */
    private static final Duration CACHE_DURATION = Duration.ofHours(2);

    /**
     * 根据项目编码查询项目完整信息（带缓存）
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    public ProjectInfoVO queryProjectInfo(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.warn("项目编码为空，无法查询项目信息");
            return null;
        }

        logger.info("开始查询项目完整信息，projectCode: {}", projectCode);

        // 构建缓存key
        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;

        try {
            // 1. 先从缓存中获取
            Object cachedData = cacheService.get(cacheKey);
            if (cachedData instanceof ProjectInfoVO) {
                logger.info("从缓存中获取到项目信息，projectCode: {}", projectCode);
                return (ProjectInfoVO) cachedData;
            }

            // 2. 缓存中没有，从数据库查询
            logger.info("缓存中未找到项目信息，从数据库查询，projectCode: {}", projectCode);
            ProjectInfoVO vo = queryProjectInfoFromDatabase(projectCode);

            // 3. 查询结果放入缓存
            if (vo != null) {
                cacheService.put(cacheKey, vo, CACHE_DURATION);
                logger.info("项目信息已放入缓存，projectCode: {}", projectCode);
            }

            return vo;

        } catch (Exception e) {
            logger.error("查询项目完整信息异常，projectCode: {}", projectCode, e);
            throw new RuntimeException("查询项目信息失败", e);
        }
    }

    /**
     * 从数据库查询项目完整信息
     *
     * @param projectCode 项目编码
     * @return 项目完整信息VO
     */
    private ProjectInfoVO queryProjectInfoFromDatabase(String projectCode) {
        logger.info("开始从数据库查询项目完整信息，projectCode: {}", projectCode);

        // 1. 查询项目基本信息，只查询状态为启用的项目
        ProjectInfoVO vo = projectInfoRepository.findByProjectCode(projectCode);
        if (vo == null) {
            logger.info("未找到项目信息，projectCode: {}", projectCode);
            return null;
        }

        // 2. 查询项目要素
        // 先看有没有临时的，如果有则使用临时，否则使用长期的。
        // 临时的结束时间需要放在缓存的过期时间中
        ProjectElements elements = projectElementsRepository.findByProjectCode(projectCode);
        if (elements != null) {
            vo.setElements(elements);
            logger.info("查询到项目要素信息，projectCode: {}", projectCode);
        } else {
            logger.info("未找到项目要素信息，projectCode: {}", projectCode);
        }

        // 3. 查询项目要素扩展
        ProjectElementsExt elementsExt = projectElementsExtRepository.findByProjectCode(projectCode);
        if (elementsExt != null) {
            vo.setElementsExt(elementsExt);
            logger.info("查询到项目要素扩展信息，projectCode: {}", projectCode);
        } else {
            logger.info("未找到项目要素扩展信息，projectCode: {}", projectCode);
        }

        // 4. 查询项目资金合同配置
        ProjectContractCapital contractCapital = projectContractCapitalRepository.findByProjectCode(projectCode);
        if (ObjectUtils.isNotEmpty(contractCapital)) {
            vo.setContractCapital(contractCapital);
            logger.info("查询到项目资金合同信息, projectCode: {}", projectCode);
        } else {
            logger.info("未找到项目资金合同信息，projectCode: {}", projectCode);
        }

        // 5. 查询项目资产合同配置
        ProjectContractFlow contractFlow = projectContractFlowRepository.findByProjectCode(projectCode);
        if (ObjectUtils.isNotEmpty(contractFlow)) {
            vo.setContractFlow(contractFlow);
            logger.info("查询到项目资产合同信息, projectCode: {}", projectCode);
        } else {
            logger.info("未找到项目资产合同信息，projectCode: {}", projectCode);
        }

        logger.info("项目完整信息查询完成，projectCode: {}", projectCode);
        return vo;
    }

    /**
     * 清除项目信息缓存
     *
     * @param projectCode 项目编码
     */
    public void clearProjectInfoCache(String projectCode) {
        if (StringUtils.isBlank(projectCode)) {
            logger.warn("项目编码为空，无法清除缓存");
            return;
        }

        String cacheKey = PROJECT_INFO_CACHE_PREFIX + projectCode;
        try {
            cacheService.delete(cacheKey);
            logger.info("已清除项目信息缓存，projectCode: {}", projectCode);
        } catch (Exception e) {
            logger.error("清除项目信息缓存异常，projectCode: {}", projectCode, e);
        }
    }

    /**
     * 批量清除项目信息缓存
     *
     * @param projectCodes 项目编码列表
     */
    public void clearProjectInfoCache(java.util.List<String> projectCodes) {
        if (projectCodes == null || projectCodes.isEmpty()) {
            logger.warn("项目编码列表为空，无法清除缓存");
            return;
        }

        for (String projectCode : projectCodes) {
            clearProjectInfoCache(projectCode);
        }
    }


}
