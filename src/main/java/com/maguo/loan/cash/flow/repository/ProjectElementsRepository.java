package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.common.ProjectElements;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 项目要素Repository
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 15:00
 */
public interface ProjectElementsRepository extends JpaRepository<ProjectElements, String> {

    /**
     * 根据项目编码查询项目要素
     * @param projectCode 项目编码
     * @return 项目要素
     */
    ProjectElements findByProjectCode(String projectCode);
}
