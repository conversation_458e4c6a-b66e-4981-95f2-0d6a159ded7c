package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.common.ProjectContractFlow;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * 项目资产合同Repository
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 15:03
 */
public interface ProjectContractFlowRepository extends JpaRepository<ProjectContractFlow, Long> {

    /**
     * 根据项目编码查询项目资产合同
     *
     * @param projectCode 项目编码
     * @return 项目资产合同
     */
    ProjectContractFlow findByProjectCode(String projectCode);
}
