package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.common.ProjectContractCapital;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * 项目资金合同Repository
 *
 * @Author: Lior
 * @CreateTime: 2025/8/18 15:02
 */
public interface ProjectContractCapitalRepository extends JpaRepository<ProjectContractCapital, Long> {

    /**
     * 根据项目编码查询项目资金合同
     *
     * @param projectCode 项目编码
     * @return 项目资金合同
     */
    ProjectContractCapital findByProjectCode(String projectCode);
}
